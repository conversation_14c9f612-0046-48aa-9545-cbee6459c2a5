# WebRTC Connection Type Detection

This document explains how the WebRTC connection type detection feature works in the participant view.

## Overview

The application now displays the type of connection being used for WebRTC communication, helping users understand their connection quality and troubleshoot potential issues.

## Connection Types

### 🔗 Local Network (WiFi) - `local`
- **Indicator**: 📶 Green text
- **Description**: Direct connection over your local network (WiFi/Ethernet)
- **Quality**: Best quality and lowest latency
- **ICE Candidates**: Both local and remote candidates are of type `host`

### 🌐 Internet (STUN) - `internet`
- **Indicator**: 🌐 Blue text  
- **Description**: Connection through the internet using STUN servers
- **Quality**: Good quality with moderate latency
- **ICE Candidates**: At least one candidate is of type `srflx` (server-reflexive)

### 🔄 TURN Server (Relay) - `relay`
- **Indicator**: 🔄 Orange text
- **Description**: Connection through a TURN relay server
- **Quality**: May have higher latency but ensures connectivity in restrictive networks
- **ICE Candidates**: At least one candidate is of type `relay`

### ❓ Detecting... - `unknown`
- **Indicator**: ❓ Gray text
- **Description**: Connection type is being detected or could not be determined
- **Quality**: Unknown

## How It Works

1. **Connection Establishment**: When a WebRTC peer connection is established, the system waits for the connection state to become 'connected'

2. **Stats Analysis**: After a 1-second delay (to ensure stats are available), the system calls `getStats()` on the peer connection

3. **Candidate Pair Analysis**: The system examines successful candidate pairs to determine the types of ICE candidates being used

4. **Type Determination**: Based on the candidate types, the connection is classified:
   - If any candidate is `relay` → TURN Server
   - If any candidate is `srflx` (and no relay) → Internet (STUN)
   - If both candidates are `host` → Local Network

5. **UI Update**: The participant interface displays the connection type with appropriate icon and color

## User Interface

### Header Display
- Shows connection status (Connected/Connecting/Disconnected)
- Shows connection type with icon and descriptive text
- Includes an info button (ℹ️) for detailed explanation

### Controls Section
- Displays connection type alongside other stream controls
- Shows the same icon and text as the header

### Info Tooltip
- Click the info button to see detailed explanation
- Explains what the connection type means
- Provides context about expected quality and latency

## Technical Implementation

### Files Modified
- `src/contexts/ConnectionContext.tsx`: Added connection type state and detection logic
- `src/components/ParticipantInterface.tsx`: Added UI components to display connection type

### Key Functions
- `analyzeConnectionType()`: Analyzes WebRTC stats to determine connection type
- `getConnectionTypeText()`: Returns human-readable connection type text
- `getConnectionTypeColor()`: Returns appropriate CSS color class
- `getConnectionTypeIcon()`: Returns emoji icon for connection type
- `getConnectionTypeDescription()`: Returns detailed explanation

## Testing

To test the different connection types:

1. **Local Network**: Test with two devices on the same WiFi network
2. **Internet (STUN)**: Test with devices on different networks
3. **TURN Relay**: Test in restrictive network environments or force TURN usage

## Troubleshooting

- If connection type shows "Detecting..." for extended periods, check browser console for WebRTC errors
- Connection type detection requires successful peer connection establishment
- Stats may not be immediately available; the system waits 1 second before analysis

## Future Enhancements

Potential improvements could include:
- Real-time connection quality metrics
- Bandwidth usage indicators
- Network latency measurements
- Connection stability indicators
