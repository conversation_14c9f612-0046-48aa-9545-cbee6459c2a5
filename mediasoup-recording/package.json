{"name": "mediasoup-recording", "description": "mediasoup demo - WebRTC recording", "version": "1.0.0", "main": "server.js", "scripts": {"prestart": "browserify client.js --outfile client-bundle.js --standalone client", "start": "node server.js"}, "repository": {"type": "git", "url": "https://github.com/Kurento/mediasoup-demos.git"}, "author": "<PERSON> <<EMAIL>> (https://github.com/j1elo)", "license": "Apache-2.0", "bugs": {"url": "https://github.com/Kurento/mediasoup-demos/issues"}, "homepage": "https://github.com/Kurento/mediasoup-demos", "dependencies": {"browserify": "^16.0.0", "express": "^4.0.0", "ffmpeg-static": "^4.0.0", "mediasoup": "^3.14.9", "mediasoup-client": "~3.6.0", "socket.io": "^2.0.0", "socket.io-client": "^2.0.0", "socket.io-promise": "^1.0.0"}, "devDependencies": {"eslint": "^6.0.0", "prettier": "~2.0.0"}}