/**
 * Layout calculation utilities for video compositing
 */

export type LayoutMode = 'grid' | 'focus' | 'pip';

export interface Viewport {
  x: number;
  y: number;
  width: number;
  height: number;
}

/**
 * Calculate viewport for a video based on layout mode and position
 */
export const calculateVideoViewport = (
  index: number,
  totalVideos: number,
  canvasWidth: number,
  canvasHeight: number,
  layout: LayoutMode
): Viewport => {
  switch (layout) {
    case 'grid': {
      const cols = Math.ceil(Math.sqrt(totalVideos));
      const rows = Math.ceil(totalVideos / cols);
      const cellWidth = canvasWidth / cols;
      const cellHeight = canvasHeight / rows;
      const col = index % cols;
      const row = Math.floor(index / cols);

      return {
        x: col * cellWidth,
        y: row * cellHeight,
        width: cellWidth,
        height: cellHeight
      };
    }

    case 'focus': {
      if (index === 0) {
        // Main video takes 75% of width
        return {
          x: 0,
          y: 0,
          width: canvasWidth * 0.75,
          height: canvasHeight
        };
      } else {
        // Sidebar videos
        const sidebarWidth = canvasWidth * 0.25;
        const sidebarVideos = totalVideos - 1;
        const itemHeight = sidebarVideos > 0 ? canvasHeight / sidebarVideos : canvasHeight;

        return {
          x: canvasWidth - sidebarWidth,
          y: (index - 1) * itemHeight,
          width: sidebarWidth,
          height: itemHeight
        };
      }
    }

    case 'pip': {
      if (index === 0) {
        // Main video takes full canvas
        return {
          x: 0,
          y: 0,
          width: canvasWidth,
          height: canvasHeight
        };
      } else {
        // Picture-in-picture videos
        const pipSize = 200;
        const margin = 20;

        return {
          x: canvasWidth - pipSize - margin,
          y: margin + (index - 1) * (pipSize + margin),
          width: pipSize,
          height: pipSize
        };
      }
    }

    default:
      return { x: 0, y: 0, width: canvasWidth, height: canvasHeight };
  }
};

/**
 * Calculate viewport for overlay video (bottom-right, 50% size)
 */
export const calculateOverlayViewport = (
  canvasWidth: number,
  canvasHeight: number
): Viewport => {
  const overlayWidth = canvasWidth * 0.5;
  const overlayHeight = canvasHeight * 0.5;

  // Position at bottom-right corner
  // Based on WebGL coordinate system where fragment shader flips Y
  return {
    x: canvasWidth - overlayWidth,   // Right side
    y: 0,                           // This gives bottom-right due to shader Y flip
    width: overlayWidth,
    height: overlayHeight
  };
};

/**
 * Calculate viewport for logo (top-right, 10% size)
 */
export const calculateLogoViewport = (
  canvasWidth: number,
  canvasHeight: number
): Viewport => {
  // Logo should be 10% of frame height and width, positioned top-right
  const logoSize = Math.min(canvasWidth, canvasHeight) * 0.1;
  const margin = logoSize * 0.2; // 20% margin from edges

  // Position at top-right corner
  // Based on WebGL coordinate system where fragment shader flips Y
  return {
    x: canvasWidth - logoSize - margin,  // Right side with margin
    y: canvasHeight - logoSize - margin, // This gives top-right due to shader Y flip
    width: logoSize,
    height: logoSize
  };
};
