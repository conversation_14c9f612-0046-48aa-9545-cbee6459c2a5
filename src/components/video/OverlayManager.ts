/**
 * Overlay management for video compositing (overlay video and logo)
 */

export class OverlayManager {
  private overlayVideoRef: HTMLVideoElement | null = null;
  private logoImageRef: HTMLImageElement | null = null;

  constructor() {
  }

  /**
   * Set up overlay video
   */
  setupOverlayVideo(overlayVideoUrl: string): void {
    if (!this.overlayVideoRef) {
      const video = document.createElement('video');
      video.autoplay = true;
      video.playsInline = true;
      video.muted = true;
      video.loop = true;
      video.crossOrigin = 'anonymous';
      video.style.position = 'absolute';
      video.style.top = '0px';
      video.style.left = '0px';
      video.style.width = '1px';
      video.style.height = '1px';
      video.style.opacity = '0.01';
      video.style.pointerEvents = 'none';
      video.style.zIndex = '-1000';

      // Add to DOM to ensure proper loading
      document.body.appendChild(video);

      this.overlayVideoRef = video;
    }

    if (this.overlayVideoRef.src !== overlayVideoUrl) {
      const video = this.overlayVideoRef;
      video.src = overlayVideoUrl;

      // Force video to load and play
      video.load();
      video.play().catch((error) => {
        console.error('❌ Failed to play overlay video:', error);
      });
    }
  }

  /**
   * Remove overlay video
   */
  removeOverlayVideo(): void {
    if (this.overlayVideoRef) {
      if (this.overlayVideoRef.parentNode) {
        this.overlayVideoRef.parentNode.removeChild(this.overlayVideoRef);
      }
      this.overlayVideoRef.src = '';
      this.overlayVideoRef = null;
    }
  }

  /**
   * Set up logo image
   */
  setupLogoImage(): void {
    if (!this.logoImageRef) {
      const image = new Image();
      image.crossOrigin = 'anonymous';
      image.style.position = 'absolute';
      image.style.top = '0px';
      image.style.left = '0px';
      image.style.width = '1px';
      image.style.height = '1px';
      image.style.opacity = '0.01';
      image.style.pointerEvents = 'none';
      image.style.zIndex = '-1000';

      // Add to DOM to ensure proper loading
      document.body.appendChild(image);

      this.logoImageRef = image;

      // Load the SVG logo
      image.onload = () => {
        // Image loaded successfully
      };

      image.onerror = (error) => {
        console.error('❌ Logo image load error:', error);
      };

      // Load and convert SVG logo for WebGL compatibility
      this.loadAndConvertSVGLogo(image);
    }
  }



  /**
   * Load and convert SVG logo to bitmap
   */
  private loadAndConvertSVGLogo(image: HTMLImageElement): void {
    fetch('/switcher-logo.svg')
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
      })
      .then(svgText => {
        // Create a blob from the SVG text
        const blob = new Blob([svgText], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);

        // Create a temporary image to render the SVG
        const tempImg = new Image();
        tempImg.onload = () => {
          // Create a canvas to convert SVG to bitmap
          const canvas = document.createElement('canvas');
          canvas.width = 128;
          canvas.height = 128;
          const ctx = canvas.getContext('2d');

          if (ctx) {
            // Clear canvas with transparent background
            ctx.clearRect(0, 0, 128, 128);

            // Draw the SVG image onto the canvas
            ctx.drawImage(tempImg, 0, 0, 128, 128);

            // Convert canvas to data URL and set as image source
            const dataUrl = canvas.toDataURL('image/png');
            image.src = dataUrl;
          }

          // Clean up the blob URL
          URL.revokeObjectURL(url);
        };

        tempImg.onerror = (error) => {
          console.error('❌ Failed to load SVG for conversion:', error);
          URL.revokeObjectURL(url);
        };

        tempImg.src = url;
      })
      .catch((error) => {
        console.error('❌ Failed to fetch SVG file:', error);
      });
  }

  /**
   * Remove logo image
   */
  removeLogoImage(): void {
    if (this.logoImageRef && this.logoImageRef.parentNode) {
      this.logoImageRef.parentNode.removeChild(this.logoImageRef);
      this.logoImageRef = null;
    }
  }

  /**
   * Get overlay video element
   */
  getOverlayVideo(): HTMLVideoElement | null {
    return this.overlayVideoRef;
  }

  /**
   * Get logo image element
   */
  getLogoImage(): HTMLImageElement | null {
    return this.logoImageRef;
  }

  /**
   * Check if overlay video is ready for rendering
   */
  isOverlayVideoReady(): boolean {
    return !!(this.overlayVideoRef &&
      this.overlayVideoRef.readyState >= this.overlayVideoRef.HAVE_CURRENT_DATA &&
      this.overlayVideoRef.videoWidth > 0 &&
      this.overlayVideoRef.videoHeight > 0);
  }

  /**
   * Check if logo image is ready for rendering
   */
  isLogoImageReady(): boolean {
    return !!(this.logoImageRef && 
      this.logoImageRef.complete && 
      this.logoImageRef.naturalWidth > 0);
  }

  /**
   * Clean up all overlay elements
   */
  cleanup(): void {
    this.removeOverlayVideo();
    this.removeLogoImage();
  }
}
