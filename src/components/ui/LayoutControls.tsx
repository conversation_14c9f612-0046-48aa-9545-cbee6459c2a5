import React from 'react';
import { Monitor, Grid, Maximize2 } from 'lucide-react';
import { LayoutMode } from '../webgl/layoutUtils';

interface LayoutControlsProps {
  layoutMode: LayoutMode;
  onLayoutModeChange: (mode: LayoutMode) => void;
  showOverlays?: boolean;
}

export const LayoutControls: React.FC<LayoutControlsProps> = ({
  layoutMode,
  onLayoutModeChange,
  showOverlays = true
}) => {
  if (!showOverlays) {
    return null;
  }

  return (
    <div className="absolute top-4 right-4 z-10 flex space-x-2">
      <button
        onClick={() => onLayoutModeChange('grid')}
        className={`p-2 rounded-lg transition-colors ${
          layoutMode === 'grid' ? 'bg-blue-500/30 text-blue-400' : 'bg-gray-700/50 text-gray-400 hover:text-white'
        }`}
        title="Grid Layout"
      >
        <Grid className="w-5 h-5" />
      </button>
      <button
        onClick={() => onLayoutModeChange('focus')}
        className={`p-2 rounded-lg transition-colors ${
          layoutMode === 'focus' ? 'bg-blue-500/30 text-blue-400' : 'bg-gray-700/50 text-gray-400 hover:text-white'
        }`}
        title="Focus Layout"
      >
        <Monitor className="w-5 h-5" />
      </button>
      <button
        onClick={() => onLayoutModeChange('pip')}
        className={`p-2 rounded-lg transition-colors ${
          layoutMode === 'pip' ? 'bg-blue-500/30 text-blue-400' : 'bg-gray-700/50 text-gray-400 hover:text-white'
        }`}
        title="Picture-in-Picture"
      >
        <Maximize2 className="w-5 h-5" />
      </button>

    </div>
  );
};
