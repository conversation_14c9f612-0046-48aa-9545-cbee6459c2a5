import React from 'react';
import { LayoutMode } from '../webgl/layoutUtils';

interface StatusOverlayProps {
  layoutMode: LayoutMode;
  peerCount: number;
  hasLocalStream: boolean;
  showOverlays?: boolean;
}

export const StatusOverlay: React.FC<StatusOverlayProps> = ({
  layoutMode,
  peerCount,
  hasLocalStream,
  showOverlays = true
}) => {
  if (!showOverlays) {
    return null;
  }

  return (
    <div className="absolute bottom-4 left-4 bg-gray-800/80 backdrop-blur-sm rounded-lg px-3 py-2">
      <span className="text-white text-sm">
        Layout: {layoutMode.toUpperCase()} • {peerCount} participants • {hasLocalStream ? 'Host' : 'No host'}
      </span>
    </div>
  );
};
