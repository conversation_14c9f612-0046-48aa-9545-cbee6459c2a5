import React, { useState } from 'react';
import { WelcomeScreen } from './components/WelcomeScreen';
import { HostInterface } from './components/HostInterface';
import { ParticipantInterface } from './components/ParticipantInterface';
import { ConnectionProvider, useConnection } from './contexts/ConnectionContext';

type AppMode = 'welcome' | 'host' | 'participant';

const AppContent: React.FC = () => {
  const [mode, setMode] = useState<AppMode>('welcome');
  const [roomId, setRoomId] = useState<string>('');
  const { leaveRoom } = useConnection();

  const handleJoinAsHost = (id: string) => {
    setRoomId(id);
    setMode('host');
  };

  const handleJoinAsParticipant = (id: string) => {
    setRoomId(id);
    setMode('participant');
  };

  const handleLeaveRoom = () => {
    // First properly leave the room (disconnect socket, clean up peers, stop streams)
    leaveRoom();
    // Then switch back to welcome screen
    setMode('welcome');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900">
      {mode === 'welcome' && (
        <WelcomeScreen
          onJoinAsHost={handleJoinAsHost}
          onJoinAsParticipant={handleJoinAsParticipant}
        />
      )}
      {mode === 'host' && (
        <HostInterface roomId={roomId} onLeave={handleLeaveRoom} />
      )}
      {mode === 'participant' && (
        <ParticipantInterface roomId={roomId} onLeave={handleLeaveRoom} />
      )}
    </div>
  );
};

const App: React.FC = () => {
  return (
    <ConnectionProvider>
      <AppContent />
    </ConnectionProvider>
  );
};

export default App;